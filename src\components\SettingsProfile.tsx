import React, { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import { useToast } from "@/hooks/use-toast";

const SettingsProfile = () => {
  const { toast } = useToast();
  const [settings, setSettings] = useState({
    // General Settings
    defaultDepartment: "Operations",

    // Notification Preferences
    emailNotifications: true,
    smsNotifications: false,
    inAppAlerts: true,

    // Access Roles
    defaultRole: "Admin",

    // Export Defaults
    exportFormat: "Excel",

    // Workflow Toggles
    autoAssignOrders: true,
    enableMarketingBanners: false,
  });

  const handleSaveChanges = () => {
    toast({
      title: "Settings Saved",
      description: "Your settings have been successfully updated.",
    });
  };

  const handleExport = (dataType: string, format: string) => {
    toast({
      title: "Export Started",
      description: `Downloading ${dataType} data as ${format.toUpperCase()}...`,
    });

    // Simulate export process with a delay
    setTimeout(() => {
      toast({
        title: "Export Complete",
        description: `${
          dataType.charAt(0).toUpperCase() + dataType.slice(1)
        } data has been successfully exported as ${format.toUpperCase()}`,
      });
    }, 2000);

    // In a real application, this would trigger the actual export functionality
    console.log(`Exporting ${dataType} data as ${format}`);
  };

  return (
    <div className="space-y-6 transition-colors duration-500 ease-in-out">
      <div>
        <h1 className="text-3xl font-bold transition-colors duration-500 ease-in-out text-gray-900 dark:text-white">
          Settings
        </h1>
        <p className="text-base transition-colors duration-500 ease-in-out text-gray-600 dark:text-gray-300">
          Configure your application preferences and workflow settings
        </p>
      </div>

      {/* Notification Preferences */}
      <Card className="transition-colors duration-500 ease-in-out bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700">
        <CardHeader>
          <CardTitle className="text-2xl transition-colors duration-500 ease-in-out text-gray-900 dark:text-white">
            Notification Preferences
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="flex items-center justify-between">
            <div>
              <Label className="text-base font-medium">
                Email Notifications
              </Label>
              <p className="text-sm text-gray-500 dark:text-gray-400">
                Receive notifications via email
              </p>
            </div>
            <Switch
              checked={settings.emailNotifications}
              onCheckedChange={(checked) => {
                setSettings((prev) => ({
                  ...prev,
                  emailNotifications: checked,
                }));
                toast({
                  title: "Setting Updated",
                  description: `Email notifications ${
                    checked ? "enabled" : "disabled"
                  }`,
                });
              }}
            />
          </div>

          <div className="flex items-center justify-between">
            <div>
              <Label className="text-base font-medium">SMS Notifications</Label>
              <p className="text-sm text-gray-500 dark:text-gray-400">
                Receive notifications via SMS
              </p>
            </div>
            <Switch
              checked={settings.smsNotifications}
              onCheckedChange={(checked) => {
                setSettings((prev) => ({ ...prev, smsNotifications: checked }));
                toast({
                  title: "Setting Updated",
                  description: `SMS notifications ${
                    checked ? "enabled" : "disabled"
                  }`,
                });
              }}
            />
          </div>

          <div className="flex items-center justify-between">
            <div>
              <Label className="text-base font-medium">In-app Alerts</Label>
              <p className="text-sm text-gray-500 dark:text-gray-400">
                Show notifications within the application
              </p>
            </div>
            <Switch
              checked={settings.inAppAlerts}
              onCheckedChange={(checked) => {
                setSettings((prev) => ({ ...prev, inAppAlerts: checked }));
                toast({
                  title: "Setting Updated",
                  description: `In-app alerts ${
                    checked ? "enabled" : "disabled"
                  }`,
                });
              }}
            />
          </div>
        </CardContent>
      </Card>

      {/* Workflow Toggles */}
      <Card className="transition-colors duration-500 ease-in-out bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700">
        <CardHeader>
          <CardTitle className="text-2xl transition-colors duration-500 ease-in-out text-gray-900 dark:text-white">
            Workflow Toggles
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="flex items-center justify-between">
            <div>
              <Label className="text-base font-medium">
                Auto-Assign Orders
              </Label>
              <p className="text-sm text-gray-500 dark:text-gray-400">
                Automatically assign new orders to available staff
              </p>
            </div>
            <Switch
              checked={settings.autoAssignOrders}
              onCheckedChange={(checked) => {
                setSettings((prev) => ({ ...prev, autoAssignOrders: checked }));
                toast({
                  title: "Setting Updated",
                  description: `Auto-assign orders ${
                    checked ? "enabled" : "disabled"
                  }`,
                });
              }}
            />
          </div>

          <div className="flex items-center justify-between">
            <div>
              <Label className="text-base font-medium">
                Enable Marketing Banners
              </Label>
              <p className="text-sm text-gray-500 dark:text-gray-400">
                Show promotional banners in the dashboard
              </p>
            </div>
            <Switch
              checked={settings.enableMarketingBanners}
              onCheckedChange={(checked) => {
                setSettings((prev) => ({
                  ...prev,
                  enableMarketingBanners: checked,
                }));
                toast({
                  title: "Setting Updated",
                  description: `Marketing banners ${
                    checked ? "enabled" : "disabled"
                  }`,
                });
              }}
            />
          </div>
        </CardContent>
      </Card>

      {/* Data Export Section */}
      <Card className="transition-colors duration-500 ease-in-out bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700">
        <CardHeader>
          <CardTitle className="text-2xl transition-colors duration-500 ease-in-out text-gray-900 dark:text-white">
            Data Export
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Products Export */}
          <div className="space-y-3">
            <h3 className="text-lg font-medium transition-colors duration-500 ease-in-out text-gray-700 dark:text-gray-300">
              Products Data
            </h3>
            <div className="flex flex-wrap gap-2">
              <Button
                onClick={() => handleExport("products", "excel")}
                className="bg-green-600 hover:bg-green-700 text-white text-base px-4 py-2"
              >
                Download as Excel
              </Button>
              <Button
                onClick={() => handleExport("products", "csv")}
                className="bg-blue-600 hover:bg-blue-700 text-white text-base px-4 py-2"
              >
                Download as CSV
              </Button>
              <Button
                onClick={() => handleExport("products", "pdf")}
                className="bg-red-600 hover:bg-red-700 text-white text-base px-4 py-2"
              >
                Download as PDF
              </Button>
              <Button
                onClick={() => handleExport("products", "word")}
                className="bg-indigo-600 hover:bg-indigo-700 text-white text-base px-4 py-2"
              >
                Download as Word
              </Button>
            </div>
          </div>

          {/* Orders Export */}
          <div className="space-y-3">
            <h3 className="text-lg font-medium transition-colors duration-500 ease-in-out text-gray-700 dark:text-gray-300">
              Orders Data
            </h3>
            <div className="flex flex-wrap gap-2">
              <Button
                onClick={() => handleExport("orders", "excel")}
                className="bg-green-600 hover:bg-green-700 text-white text-base px-4 py-2"
              >
                Download as Excel
              </Button>
              <Button
                onClick={() => handleExport("orders", "csv")}
                className="bg-blue-600 hover:bg-blue-700 text-white text-base px-4 py-2"
              >
                Download as CSV
              </Button>
              <Button
                onClick={() => handleExport("orders", "pdf")}
                className="bg-red-600 hover:bg-red-700 text-white text-base px-4 py-2"
              >
                Download as PDF
              </Button>
              <Button
                onClick={() => handleExport("orders", "word")}
                className="bg-indigo-600 hover:bg-indigo-700 text-white text-base px-4 py-2"
              >
                Download as Word
              </Button>
            </div>
          </div>

          {/* Staff Export */}
          <div className="space-y-3">
            <h3 className="text-lg font-medium transition-colors duration-500 ease-in-out text-gray-700 dark:text-gray-300">
              Staff Data
            </h3>
            <div className="flex flex-wrap gap-2">
              <Button
                onClick={() => handleExport("staff", "excel")}
                className="bg-green-600 hover:bg-green-700 text-white text-base px-4 py-2"
              >
                Download as Excel
              </Button>
              <Button
                onClick={() => handleExport("staff", "csv")}
                className="bg-blue-600 hover:bg-blue-700 text-white text-base px-4 py-2"
              >
                Download as CSV
              </Button>
              <Button
                onClick={() => handleExport("staff", "pdf")}
                className="bg-red-600 hover:bg-red-700 text-white text-base px-4 py-2"
              >
                Download as PDF
              </Button>
              <Button
                onClick={() => handleExport("staff", "word")}
                className="bg-indigo-600 hover:bg-indigo-700 text-white text-base px-4 py-2"
              >
                Download as Word
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Save Button */}
      <div className="flex justify-end">
        <Button
          onClick={handleSaveChanges}
          className="text-white bg-[#FFD700] hover:bg-[#E6C200] transition-all duration-300 ease-in-out text-base px-8 py-4"
        >
          Save Changes
        </Button>
      </div>
    </div>
  );
};

export default SettingsProfile;
