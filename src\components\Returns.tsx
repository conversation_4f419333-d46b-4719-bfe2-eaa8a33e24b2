import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { useToast } from "@/hooks/use-toast";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import AnimatedNumber from "./AnimatedNumber";
import AnimatedText from "./AnimatedText";

const returnStats = [
  { title: "Total Returns", value: "4", color: "text-gray-900" },
  { title: "Pending", value: "2", color: "text-orange-600" },
  { title: "Approved", value: "1", color: "text-green-600" },
  { title: "Refunded", value: "1", color: "text-blue-600" },
];

const returnsData = [
  {
    id: 1,
    orderNumber: "ORD-2024-001",
    customer: "<PERSON><PERSON>",
    email: "<EMAIL>",
    product: "<PERSON><PERSON><PERSON>",
    quantity: 2,
    amount: "₹498",
    reason: "Defective product",
    status: "Pending",
    requestDate: "2024-01-20",
  },
  {
    id: 2,
    orderNumber: "ORD-2024-002",
    customer: "Priya Sharma",
    email: "<EMAIL>",
    product: "Ashwagandha Capsules",
    quantity: 1,
    amount: "₹599",
    reason: "Wrong item received",
    status: "Approved",
    requestDate: "2024-01-19",
  },
  {
    id: 3,
    orderNumber: "ORD-2024-003",
    customer: "Amit Patel",
    email: "<EMAIL>",
    product: "Triphala Churna",
    quantity: 1,
    amount: "₹149",
    reason: "Not satisfied with quality",
    status: "Refunded",
    requestDate: "2024-01-18",
  },
  {
    id: 4,
    orderNumber: "ORD-2024-004",
    customer: "Sneha Reddy",
    email: "<EMAIL>",
    product: "Giloy Juice",
    quantity: 1,
    amount: "₹349",
    reason: "Damaged packaging",
    status: "Pending",
    requestDate: "2024-01-17",
  },
];

const Returns = () => {
  const { toast } = useToast();
  const [returns, setReturns] = useState(returnsData);

  const handleStatusChange = (id: number, newStatus: string) => {
    setReturns((prev) =>
      prev.map((item) =>
        item.id === id ? { ...item, status: newStatus } : item
      )
    );

    toast({
      title: "Status Updated",
      description: `Return status changed to ${newStatus}`,
    });
  };

  const handleStatusToggle = (id: number, currentStatus: string) => {
    const statusCycle = ["Pending", "Approved", "Refunded"];
    const currentIndex = statusCycle.indexOf(currentStatus);
    const nextStatus = statusCycle[(currentIndex + 1) % statusCycle.length];
    handleStatusChange(id, nextStatus);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "Pending":
        return "bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200";
      case "Approved":
        return "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200";
      case "In Transit":
        return "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200";
      case "Rejected":
        return "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200";
      case "Refunded":
        return "bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200";
      default:
        return "bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200";
    }
  };

  return (
    <div className="space-y-6 transition-colors duration-500 ease-in-out">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white transition-colors duration-500 ease-in-out">
            Returns Management
          </h1>
          <p className="text-lg text-gray-600 dark:text-gray-300 transition-colors duration-500 ease-in-out">
            Handle product returns and refund requests
          </p>
        </div>
      </div>

      {/* Return Statistics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {returnStats.map((stat, index) => (
          <Card
            key={index}
            className="transition-colors duration-500 ease-in-out bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700"
          >
            <CardContent className="p-6">
              <div className="space-y-2">
                <p className="text-lg font-medium text-gray-600 dark:text-gray-400 transition-colors duration-500 ease-in-out">
                  {stat.title}
                </p>
                <AnimatedNumber
                  value={stat.value}
                  className="text-4xl font-bold"
                />
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Returns Table */}
      <Card className="transition-colors duration-500 ease-in-out bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700">
        <CardHeader>
          <CardTitle className="text-2xl text-gray-900 dark:text-white transition-colors duration-500 ease-in-out">
            Return Requests
          </CardTitle>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead className="text-lg text-center text-gray-900 dark:text-white transition-colors duration-500 ease-in-out">
                  Order & Customer
                </TableHead>
                <TableHead className="text-lg text-center text-gray-900 dark:text-white transition-colors duration-500 ease-in-out">
                  Product
                </TableHead>
                <TableHead className="text-lg text-center text-gray-900 dark:text-white transition-colors duration-500 ease-in-out">
                  Amount
                </TableHead>
                <TableHead className="text-lg text-center text-gray-900 dark:text-white transition-colors duration-500 ease-in-out">
                  Reason
                </TableHead>
                <TableHead className="text-lg text-center text-gray-900 dark:text-white transition-colors duration-500 ease-in-out">
                  Status
                </TableHead>
                <TableHead className="text-lg text-center text-gray-900 dark:text-white transition-colors duration-500 ease-in-out">
                  Date
                </TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {returns.map((item) => (
                <TableRow key={item.id}>
                  <TableCell className="text-center">
                    <div className="flex flex-col items-center">
                      <AnimatedText className="font-medium text-lg">
                        {item.orderNumber}
                      </AnimatedText>
                      <AnimatedText className="text-base font-medium">
                        {item.customer}
                      </AnimatedText>
                      <span className="text-base text-gray-500 dark:text-gray-400 transition-colors duration-500 ease-in-out">
                        {item.email}
                      </span>
                    </div>
                  </TableCell>
                  <TableCell className="text-center">
                    <div className="flex flex-col items-center">
                      <span className="font-medium text-lg text-gray-900 dark:text-white transition-colors duration-500 ease-in-out">
                        {item.product}
                      </span>
                      <span className="text-base text-gray-500 dark:text-gray-400 transition-colors duration-500 ease-in-out">
                        Qty: {item.quantity}
                      </span>
                    </div>
                  </TableCell>
                  <TableCell className="text-center font-medium text-lg text-gray-900 dark:text-white transition-colors duration-500 ease-in-out">
                    {item.amount}
                  </TableCell>
                  <TableCell className="text-center">
                    <span className="text-lg text-gray-700 dark:text-gray-300 transition-colors duration-500 ease-in-out">
                      {item.reason}
                    </span>
                  </TableCell>
                  <TableCell className="text-center">
                    <Button
                      variant="outline"
                      onClick={() => handleStatusToggle(item.id, item.status)}
                      className="w-full max-w-[140px] text-base bg-red-50 hover:bg-red-100 border-red-200 text-red-700 dark:bg-black dark:hover:bg-gray-800 dark:border-gray-600 dark:text-white"
                    >
                      <Badge
                        className={`${getStatusColor(item.status)} text-base`}
                      >
                        {item.status}
                      </Badge>
                    </Button>
                  </TableCell>
                  <TableCell className="text-center text-lg text-gray-600 dark:text-gray-400 transition-colors duration-500 ease-in-out">
                    {item.requestDate}
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </div>
  );
};

export default Returns;
