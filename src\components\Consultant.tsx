import { useState, useMemo } from "react";
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { But<PERSON> } from "@/components/ui/button";
import {
  Star,
  MapPin,
  Clock,
  Phone,
  Calendar,
  User,
  Activity,
} from "lucide-react";
import AnimatedNumber from "./AnimatedNumber";
import AnimatedText from "./AnimatedText";

// Sample patient data
const patientsData = [
  {
    id: "PT001",
    name: "<PERSON><PERSON>",
    age: 32,
    gender: "F",
    appointmentDate: "2024-07-20",
    appointmentTime: "10:00 AM",
    contact: "+91 **********",
    healthConcern: "Digestive Issues",
    prescriptionGiven: "Yes",
    status: "Completed",
  },
  {
    id: "PT002",
    name: "<PERSON><PERSON><PERSON>",
    age: 45,
    gender: "M",
    appointmentDate: "2024-07-20",
    appointmentTime: "11:00 AM",
    contact: "+91 **********",
    healthConcern: "Joint Pain",
    prescriptionGiven: "Yes",
    status: "Completed",
  },
  {
    id: "PT003",
    name: "Radha Krishnan",
    age: 28,
    gender: "F",
    appointmentDate: "2024-07-21",
    appointmentTime: "09:30 AM",
    contact: "+91 **********",
    healthConcern: "Stress Management",
    prescriptionGiven: "No",
    status: "Pending",
  },
  {
    id: "PT004",
    name: "Vikram Singh",
    age: 38,
    gender: "M",
    appointmentDate: "2024-07-21",
    appointmentTime: "02:00 PM",
    contact: "+91 **********",
    healthConcern: "Skin Problems",
    prescriptionGiven: "Yes",
    status: "Follow-up",
  },
  {
    id: "PT005",
    name: "Meera Patel",
    age: 55,
    gender: "F",
    appointmentDate: "2024-07-22",
    appointmentTime: "10:30 AM",
    contact: "+91 **********",
    healthConcern: "Diabetes Management",
    prescriptionGiven: "Yes",
    status: "Pending",
  },
];

const Consultant = () => {
  const [patients, setPatients] = useState(patientsData);
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState("all");
  const [dateFilter, setDateFilter] = useState("all");
  const [followUpFilter, setFollowUpFilter] = useState("all");

  // Filter patients based on search and filters
  const filteredPatients = useMemo(() => {
    return patients.filter((patient) => {
      const matchesSearch =
        patient.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        patient.contact.includes(searchTerm);
      const matchesStatus =
        statusFilter === "all" ||
        patient.status.toLowerCase() === statusFilter.toLowerCase();
      const matchesDate =
        dateFilter === "all" || patient.appointmentDate === dateFilter;
      const matchesFollowUp =
        followUpFilter === "all" ||
        (followUpFilter === "due" && patient.status === "Follow-up");

      return matchesSearch && matchesStatus && matchesDate && matchesFollowUp;
    });
  }, [patients, searchTerm, statusFilter, dateFilter, followUpFilter]);

  // Calculate dynamic stats
  const consultantStats = useMemo(() => {
    const totalPatients = patients.length;
    const appointmentsToday = patients.filter(
      (p) => p.appointmentDate === "2024-07-20"
    ).length;
    const followUpsScheduled = patients.filter(
      (p) => p.status === "Follow-up"
    ).length;
    const completedConsultations = patients.filter(
      (p) => p.status === "Completed"
    ).length;

    return [
      {
        title: "Total Patients",
        value: "250",
        color: "text-gray-900",
        icon: User,
      },
      {
        title: "Appointments Today",
        value: "15",
        color: "text-blue-600",
        icon: Calendar,
      },
      {
        title: "Follow-ups Scheduled",
        value: "8",
        color: "text-orange-600",
        icon: Activity,
      },
      {
        title: "Total Consultations Completed",
        value: "200",
        color: "text-green-600",
        icon: Activity,
      },
    ];
  }, [patients]);

  const clearFilters = () => {
    setSearchTerm("");
    setStatusFilter("all");
    setDateFilter("all");
    setFollowUpFilter("all");
  };

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      Completed:
        "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200",
      Pending:
        "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200",
      "Follow-up":
        "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200",
    };
    return (
      statusConfig[status] ||
      "bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200"
    );
  };

  return (
    <div className="space-y-4 transition-colors duration-500 ease-in-out">
      <div className="flex items-center justify-between pt-2">
        <div>
          <h1 className="text-3xl font-bold transition-colors duration-500 ease-in-out text-gray-900 dark:text-white">
            Consultant
          </h1>
          <p className="text-lg transition-colors duration-500 ease-in-out text-gray-600 dark:text-gray-300">
            Manage patient interactions and consultation tracking for Dr. Kumar
            Clinic.
          </p>
        </div>
      </div>

      {/* Clinic Details Section */}
      <Card className="transition-colors duration-500 ease-in-out bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700">
        <CardContent className="p-6">
          <div className="flex flex-col sm:flex-row items-center sm:items-start gap-6">
            {/* Clinic Logo */}
            <div className="w-20 h-20 bg-green-100 dark:bg-green-900 rounded-full flex items-center justify-center shadow-lg shrink-0">
              <div className="w-16 h-16 bg-gradient-to-br from-green-500 to-green-700 rounded-full flex items-center justify-center">
                <div className="text-white text-2xl">
                  <svg
                    width="32"
                    height="32"
                    viewBox="0 0 24 24"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                    className="text-white"
                  >
                    <path
                      d="M12 2L13.09 8.26L20 9L13.09 9.74L12 16L10.91 9.74L4 9L10.91 8.26L12 2Z"
                      fill="currentColor"
                    />
                    <path
                      d="M12 12C14.21 12 16 10.21 16 8C16 5.79 14.21 4 12 4C9.79 4 8 5.79 8 8C8 10.21 9.79 12 12 12Z"
                      fill="currentColor"
                      opacity="0.7"
                    />
                    <path
                      d="M12 14C8.69 14 6 16.69 6 20H18C18 16.69 15.31 14 12 14Z"
                      fill="currentColor"
                      opacity="0.5"
                    />
                  </svg>
                </div>
              </div>
            </div>

            {/* Clinic Information */}
            <div className="flex-1 text-center sm:text-left">
              <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
                Dr. Kumar Clinic
              </h2>
              <p className="text-lg text-gray-600 dark:text-gray-300 mb-3">
                Ayurvedic Consultant
              </p>
              <div className="flex flex-wrap items-center justify-center sm:justify-start gap-4 text-sm text-gray-500 dark:text-gray-400">
                <span>5 years experience</span>
                <div className="flex items-center gap-1">
                  <Star className="w-4 h-4 fill-yellow-400 text-yellow-400" />
                  <span>4.4 rating</span>
                </div>
                <div className="flex items-center gap-1">
                  <MapPin className="w-4 h-4" />
                  <span>Gwalior, India</span>
                </div>
                <div className="flex items-center gap-1">
                  <Clock className="w-4 h-4" />
                  <span>Available: Mon-Fri, 9 AM - 6 PM</span>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Stats Overview Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {consultantStats.map((stat, index) => (
          <Card
            key={index}
            className="transition-colors duration-500 ease-in-out bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700"
          >
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div className="space-y-2">
                  <p className="text-lg font-medium transition-colors duration-500 ease-in-out text-gray-600 dark:text-gray-400">
                    {stat.title}
                  </p>
                  <AnimatedNumber
                    value={stat.value}
                    className="text-3xl font-bold"
                  />
                </div>
                <stat.icon className={`w-8 h-8 ${stat.color}`} />
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Patient Management Section */}
      <Card className="transition-colors duration-500 ease-in-out bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700">
        <CardContent className="p-6">
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2 transition-colors duration-500 ease-in-out text-gray-700 dark:text-gray-300">
                <span className="font-medium text-xl">
                  🔍 Search & Filter Patients
                </span>
              </div>
              <Button
                variant="outline"
                onClick={clearFilters}
                className="text-base"
              >
                Clear Filters
              </Button>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <div>
                <label className="block text-lg font-medium transition-colors duration-500 ease-in-out text-gray-700 dark:text-gray-300 mb-2">
                  Search Patients
                </label>
                <Input
                  placeholder="Search by name or phone..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="transition-colors duration-500 ease-in-out bg-white dark:bg-gray-700 border-gray-300 dark:border-gray-600 text-base"
                />
              </div>
              <div>
                <label className="block text-base font-medium transition-colors duration-500 ease-in-out text-gray-700 dark:text-gray-300 mb-2">
                  Appointment Date
                </label>
                <Select value={dateFilter} onValueChange={setDateFilter}>
                  <SelectTrigger>
                    <SelectValue placeholder="Filter by date" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Dates</SelectItem>
                    <SelectItem value="2024-07-20">Today</SelectItem>
                    <SelectItem value="2024-07-21">Tomorrow</SelectItem>
                    <SelectItem value="2024-07-22">Day After</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div>
                <label className="block text-base font-medium transition-colors duration-500 ease-in-out text-gray-700 dark:text-gray-300 mb-2">
                  Status
                </label>
                <Select value={statusFilter} onValueChange={setStatusFilter}>
                  <SelectTrigger>
                    <SelectValue placeholder="Filter by status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Status</SelectItem>
                    <SelectItem value="pending">Pending</SelectItem>
                    <SelectItem value="completed">Completed</SelectItem>
                    <SelectItem value="follow-up">Follow-up</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div>
                <label className="block text-base font-medium transition-colors duration-500 ease-in-out text-gray-700 dark:text-gray-300 mb-2">
                  Follow-up Due
                </label>
                <Select
                  value={followUpFilter}
                  onValueChange={setFollowUpFilter}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Filter follow-ups" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Patients</SelectItem>
                    <SelectItem value="due">Follow-up Due</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="text-base text-gray-500 dark:text-gray-400">
              Showing {filteredPatients.length} of {patients.length} patients
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Patient Management Table */}
      <Card className="transition-colors duration-500 ease-in-out bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700">
        <CardHeader>
          <CardTitle className="transition-colors duration-500 ease-in-out text-gray-900 dark:text-white text-2xl">
            Patient Management
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {/* Table Header - Hidden on mobile, shown on larger screens */}
            <div className="hidden lg:grid grid-cols-8 gap-4 p-4 bg-gray-50 dark:bg-gray-700 rounded-lg font-medium transition-colors duration-500 ease-in-out text-gray-700 dark:text-gray-300 text-lg border-b border-gray-200 dark:border-gray-600">
              <div className="text-center">Patient ID</div>
              <div className="text-center">Name</div>
              <div className="text-center">Age / Gender</div>
              <div className="text-center">Appointment Date & Time</div>
              <div className="text-center">Contact</div>
              <div className="text-center">Health Concern</div>
              <div className="text-center">Prescription Given</div>
              <div className="text-center">Status</div>
            </div>

            {/* Table Rows */}
            <div className="space-y-3">
              {filteredPatients.map((patient, index) => (
                <div
                  key={index}
                  className="lg:grid lg:grid-cols-8 gap-4 p-4 border border-gray-200 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 lg:items-center transition-colors duration-500 ease-in-out"
                >
                  <div className="text-center">
                    <AnimatedText className="font-medium text-lg text-blue-600 dark:text-blue-400">
                      {patient.id}
                    </AnimatedText>
                  </div>
                  <div className="text-center">
                    <AnimatedText className="font-medium text-lg text-gray-900 dark:text-white">
                      {patient.name}
                    </AnimatedText>
                  </div>
                  <div className="text-center">
                    <span className="text-lg text-gray-600 dark:text-gray-300">
                      {patient.age}/{patient.gender}
                    </span>
                  </div>
                  <div className="text-center">
                    <div className="flex flex-col items-center">
                      <span className="font-medium text-lg text-gray-900 dark:text-white">
                        {patient.appointmentDate}
                      </span>
                      <span className="text-base text-gray-500 dark:text-gray-400">
                        {patient.appointmentTime}
                      </span>
                    </div>
                  </div>
                  <div className="text-center">
                    <span className="text-lg text-gray-600 dark:text-gray-300">
                      {patient.contact}
                    </span>
                  </div>
                  <div className="text-center">
                    <span className="text-lg text-gray-600 dark:text-gray-300">
                      {patient.healthConcern}
                    </span>
                  </div>
                  {/* Mobile Card Layout */}
                  <div className="lg:hidden space-y-3">
                    <div className="flex justify-between items-center">
                      <AnimatedText className="font-medium text-lg text-gray-900 dark:text-white">
                        {patient.name}
                      </AnimatedText>
                      <Badge
                        className={`text-sm ${getStatusBadge(patient.status)}`}
                      >
                        {patient.status}
                      </Badge>
                    </div>
                    <div className="grid grid-cols-2 gap-3 text-sm">
                      <div>
                        <span className="text-gray-500 dark:text-gray-400">
                          ID:
                        </span>
                        <span className="ml-2 font-medium text-blue-600 dark:text-blue-400">
                          {patient.id}
                        </span>
                      </div>
                      <div>
                        <span className="text-gray-500 dark:text-gray-400">
                          Age/Gender:
                        </span>
                        <span className="ml-2">
                          {patient.age}/{patient.gender}
                        </span>
                      </div>
                      <div>
                        <span className="text-gray-500 dark:text-gray-400">
                          Date:
                        </span>
                        <span className="ml-2">{patient.appointmentDate}</span>
                      </div>
                      <div>
                        <span className="text-gray-500 dark:text-gray-400">
                          Time:
                        </span>
                        <span className="ml-2">{patient.appointmentTime}</span>
                      </div>
                      <div>
                        <span className="text-gray-500 dark:text-gray-400">
                          Contact:
                        </span>
                        <span className="ml-2">{patient.contact}</span>
                      </div>
                      <div>
                        <span className="text-gray-500 dark:text-gray-400">
                          Prescription:
                        </span>
                        <Badge
                          className={`ml-2 text-xs ${
                            patient.prescriptionGiven === "Yes"
                              ? "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200"
                              : "bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200"
                          }`}
                        >
                          {patient.prescriptionGiven}
                        </Badge>
                      </div>
                    </div>
                    <div>
                      <span className="text-gray-500 dark:text-gray-400">
                        Health Concern:
                      </span>
                      <span className="ml-2">{patient.healthConcern}</span>
                    </div>
                  </div>

                  {/* Desktop Table Layout */}
                  <div className="hidden lg:contents">
                    <div className="text-center">
                      <AnimatedText className="font-medium text-lg text-blue-600 dark:text-blue-400">
                        {patient.id}
                      </AnimatedText>
                    </div>
                    <div className="text-center">
                      <AnimatedText className="font-medium text-lg text-gray-900 dark:text-white">
                        {patient.name}
                      </AnimatedText>
                    </div>
                    <div className="text-center">
                      <span className="text-lg text-gray-600 dark:text-gray-300">
                        {patient.age}/{patient.gender}
                      </span>
                    </div>
                    <div className="text-center">
                      <div className="flex flex-col items-center">
                        <span className="font-medium text-lg text-gray-900 dark:text-white">
                          {patient.appointmentDate}
                        </span>
                        <span className="text-base text-gray-500 dark:text-gray-400">
                          {patient.appointmentTime}
                        </span>
                      </div>
                    </div>
                    <div className="text-center">
                      <span className="text-lg text-gray-600 dark:text-gray-300">
                        {patient.contact}
                      </span>
                    </div>
                    <div className="text-center">
                      <span className="text-lg text-gray-600 dark:text-gray-300">
                        {patient.healthConcern}
                      </span>
                    </div>
                    <div className="text-center">
                      <Badge
                        className={`text-base ${
                          patient.prescriptionGiven === "Yes"
                            ? "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200"
                            : "bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200"
                        }`}
                      >
                        {patient.prescriptionGiven}
                      </Badge>
                    </div>
                    <div className="text-center">
                      <Badge
                        className={`text-base ${getStatusBadge(
                          patient.status
                        )}`}
                      >
                        {patient.status}
                      </Badge>
                    </div>
                  </div>
                </div>
              ))}
            </div>

            {filteredPatients.length === 0 && (
              <div className="text-center py-8">
                <p className="text-lg text-gray-500 dark:text-gray-400">
                  No patients found matching your search criteria.
                </p>
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default Consultant;
