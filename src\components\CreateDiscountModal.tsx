import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>eader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { X } from "lucide-react";
import { useState } from "react";
import { useToast } from "@/hooks/use-toast";
import {
  PRODUCT_CATEGORIES,
  PRODUCTS_LIST,
  isValidCategory,
  isValidProduct,
  getCategoryForProduct,
} from "@/constants/products";

interface CreateDiscountModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: (discountData: any) => void;
}

const CreateDiscountModal = ({
  isOpen,
  onClose,
  onSave,
}: CreateDiscountModalProps) => {
  const { toast } = useToast();
  const [formData, setFormData] = useState({
    productName: "",
    description: "",
    category: "",
    discountType: "Percentage",
    discountPercentage: "",
    minimumOrderAmount: "",
    maximumDiscount: "",
    usageLimit: "",
    validFrom: "",
    validTo: "",
    status: "Active",
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    // Validate required fields
    if (
      !formData.productName ||
      !formData.description ||
      !formData.discountPercentage ||
      !formData.validFrom ||
      !formData.validTo
    ) {
      toast({
        title: "Validation Error",
        description: "Please fill in all required fields",
        variant: "destructive",
      });
      return;
    }

    // Validate product name exists in our product list
    if (!isValidProduct(formData.productName)) {
      toast({
        title: "Invalid Product",
        description: "Please select a valid product from the list",
        variant: "destructive",
      });
      return;
    }

    // Validate category if provided
    if (formData.category && !isValidCategory(formData.category)) {
      toast({
        title: "Invalid Category",
        description: "Please select a valid category",
        variant: "destructive",
      });
      return;
    }

    // Auto-set category based on product if not provided
    const productCategory = getCategoryForProduct(formData.productName);
    const finalCategory = formData.category || productCategory || "";

    onSave({
      ...formData,
      category: finalCategory,
      code: `${formData.productName
        .replace(/\s+/g, "")
        .toUpperCase()}${Math.floor(Math.random() * 1000)}`,
      type: formData.discountType,
      value: parseInt(formData.discountPercentage),
      minOrder: parseInt(formData.minimumOrderAmount) || 0,
      maxDiscount: parseInt(formData.maximumDiscount) || 0,
      usageLimit: parseInt(formData.usageLimit) || 0,
      used: 0,
      id: Date.now(),
    });

    toast({
      title: "Success",
      description: "Discount created successfully",
    });

    onClose();
    setFormData({
      productName: "",
      description: "",
      category: "",
      discountType: "Percentage",
      discountPercentage: "",
      minimumOrderAmount: "",
      maximumDiscount: "",
      usageLimit: "",
      validFrom: "",
      validTo: "",
      status: "Active",
    });

    toast({
      title: "Discount Created",
      description: "New discount has been successfully created",
    });
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700">
        <DialogHeader>
          <DialogTitle className="text-2xl text-gray-900 dark:text-white transition-colors duration-500 ease-in-out">
            Create Discount
          </DialogTitle>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label
                htmlFor="productName"
                className="text-lg font-medium text-gray-700 dark:text-gray-300 transition-colors duration-500 ease-in-out"
              >
                Product Name *
              </Label>
              <Select
                value={formData.productName}
                onValueChange={(value) => {
                  setFormData((prev) => ({
                    ...prev,
                    productName: value,
                  }));
                  // Auto-set category when product is selected
                  const productCategory = getCategoryForProduct(value);
                  if (productCategory) {
                    setFormData((prev) => ({
                      ...prev,
                      category: productCategory,
                    }));
                  }
                }}
              >
                <SelectTrigger className="transition-colors duration-500 ease-in-out bg-white dark:bg-gray-700 border-gray-300 dark:border-gray-600 text-base">
                  <SelectValue placeholder="Select product" />
                </SelectTrigger>
                <SelectContent>
                  {PRODUCTS_LIST.map((product) => (
                    <SelectItem key={product.id} value={product.name}>
                      {product.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label
                htmlFor="category"
                className="text-lg font-medium text-gray-700 dark:text-gray-300 transition-colors duration-500 ease-in-out"
              >
                Category *
              </Label>
              <Select
                value={formData.category}
                onValueChange={(value) =>
                  setFormData((prev) => ({ ...prev, category: value }))
                }
              >
                <SelectTrigger className="transition-colors duration-500 ease-in-out bg-white dark:bg-gray-700 border-gray-300 dark:border-gray-600 text-base">
                  <SelectValue placeholder="Select category" />
                </SelectTrigger>
                <SelectContent>
                  {PRODUCT_CATEGORIES.map((category) => (
                    <SelectItem key={category} value={category}>
                      {category}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="space-y-2">
            <Label
              htmlFor="description"
              className="text-lg font-medium text-gray-700 dark:text-gray-300 transition-colors duration-500 ease-in-out"
            >
              Description *
            </Label>
            <Textarea
              id="description"
              value={formData.description}
              onChange={(e) =>
                setFormData((prev) => ({
                  ...prev,
                  description: e.target.value,
                }))
              }
              placeholder="Enter description"
              className="transition-colors duration-500 ease-in-out bg-white dark:bg-gray-700 border-gray-300 dark:border-gray-600 text-base px-4 py-2 min-h-[80px]"
              required
            />
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label
                htmlFor="discountType"
                className="text-lg font-medium text-gray-700 dark:text-gray-300 transition-colors duration-500 ease-in-out"
              >
                Discount Type *
              </Label>
              <Select
                value={formData.discountType}
                onValueChange={(value) =>
                  setFormData((prev) => ({ ...prev, discountType: value }))
                }
              >
                <SelectTrigger className="transition-colors duration-500 ease-in-out bg-white dark:bg-gray-700 border-gray-300 dark:border-gray-600 text-base">
                  <SelectValue placeholder="Select discount type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="Percentage">Percentage</SelectItem>
                  <SelectItem value="Fixed">Fixed Amount</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label
                htmlFor="discountPercentage"
                className="text-lg font-medium text-gray-700 dark:text-gray-300 transition-colors duration-500 ease-in-out"
              >
                {formData.discountType === "Percentage"
                  ? "Discount Percentage *"
                  : "Discount Amount *"}
              </Label>
              <Input
                id="discountPercentage"
                type="number"
                value={formData.discountPercentage}
                onChange={(e) =>
                  setFormData((prev) => ({
                    ...prev,
                    discountPercentage: e.target.value,
                  }))
                }
                placeholder={
                  formData.discountType === "Percentage"
                    ? "Enter percentage"
                    : "Enter amount"
                }
                className="transition-colors duration-500 ease-in-out bg-white dark:bg-gray-700 border-gray-300 dark:border-gray-600 text-base px-4 py-2"
                required
              />
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label
                htmlFor="minimumOrderAmount"
                className="text-lg font-medium text-gray-700 dark:text-gray-300 transition-colors duration-500 ease-in-out"
              >
                Minimum Order Amount
              </Label>
              <Input
                id="minimumOrderAmount"
                type="number"
                value={formData.minimumOrderAmount}
                onChange={(e) =>
                  setFormData((prev) => ({
                    ...prev,
                    minimumOrderAmount: e.target.value,
                  }))
                }
                placeholder="Enter minimum order amount"
                className="transition-colors duration-500 ease-in-out bg-white dark:bg-gray-700 border-gray-300 dark:border-gray-600 text-base px-4 py-2"
              />
            </div>

            <div className="space-y-2">
              <Label
                htmlFor="maximumDiscount"
                className="text-lg font-medium text-gray-700 dark:text-gray-300 transition-colors duration-500 ease-in-out"
              >
                Maximum Discount
              </Label>
              <Input
                id="maximumDiscount"
                type="number"
                value={formData.maximumDiscount}
                onChange={(e) =>
                  setFormData((prev) => ({
                    ...prev,
                    maximumDiscount: e.target.value,
                  }))
                }
                placeholder="Enter maximum discount amount"
                className="transition-colors duration-500 ease-in-out bg-white dark:bg-gray-700 border-gray-300 dark:border-gray-600 text-base px-4 py-2"
              />
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label
                htmlFor="usageLimit"
                className="text-lg font-medium text-gray-700 dark:text-gray-300 transition-colors duration-500 ease-in-out"
              >
                Usage Limit
              </Label>
              <Input
                id="usageLimit"
                type="number"
                value={formData.usageLimit}
                onChange={(e) =>
                  setFormData((prev) => ({
                    ...prev,
                    usageLimit: e.target.value,
                  }))
                }
                placeholder="Enter usage limit"
                className="transition-colors duration-500 ease-in-out bg-white dark:bg-gray-700 border-gray-300 dark:border-gray-600 text-base px-4 py-2"
              />
            </div>

            <div className="space-y-2">
              <Label
                htmlFor="validFrom"
                className="text-lg font-medium text-gray-700 dark:text-gray-300 transition-colors duration-500 ease-in-out"
              >
                Valid From
              </Label>
              <Input
                id="validFrom"
                type="date"
                value={formData.validFrom}
                onChange={(e) =>
                  setFormData((prev) => ({
                    ...prev,
                    validFrom: e.target.value,
                  }))
                }
                className="transition-colors duration-500 ease-in-out bg-white dark:bg-gray-700 border-gray-300 dark:border-gray-600 text-base px-4 py-2"
              />
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label
                htmlFor="validTo"
                className="text-lg font-medium text-gray-700 dark:text-gray-300 transition-colors duration-500 ease-in-out"
              >
                Valid To
              </Label>
              <Input
                id="validTo"
                type="date"
                value={formData.validTo}
                onChange={(e) =>
                  setFormData((prev) => ({
                    ...prev,
                    validTo: e.target.value,
                  }))
                }
                className="transition-colors duration-500 ease-in-out bg-white dark:bg-gray-700 border-gray-300 dark:border-gray-600 text-base px-4 py-2"
              />
            </div>

            <div className="space-y-2">
              <Label
                htmlFor="status"
                className="text-lg font-medium text-gray-700 dark:text-gray-300 transition-colors duration-500 ease-in-out"
              >
                Status
              </Label>
              <Select
                value={formData.status}
                onValueChange={(value) =>
                  setFormData((prev) => ({ ...prev, status: value }))
                }
              >
                <SelectTrigger className="transition-colors duration-500 ease-in-out bg-white dark:bg-gray-700 border-gray-300 dark:border-gray-600 text-base">
                  <SelectValue placeholder="Select status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="Active">Active</SelectItem>
                  <SelectItem value="Inactive">Inactive</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="flex justify-end gap-3 pt-6">
            <Button
              type="button"
              variant="outline"
              onClick={onClose}
              className="px-6 py-2 text-base transition-colors duration-500 ease-in-out"
            >
              Cancel
            </Button>
            <Button
              type="submit"
              className="bg-[#FFD700] hover:bg-[#E6C200] text-black px-6 py-2 text-base transition-colors duration-300 ease-in-out"
            >
              Create Discount
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
};

export default CreateDiscountModal;
