import React, { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>it<PERSON> } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Camera } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import ImageCropModal from "../ImageCropModal";
import { useProfile } from "@/contexts/ProfileContext";

const ProfileTab = () => {
  const { toast } = useToast();
  const { profileData, updateProfileData, updateProfileImage, getInitials } =
    useProfile();
  const [userRole] = useState("admin"); // This would come from auth context
  const [isCropModalOpen, setIsCropModalOpen] = useState(false);

  // Check if user is Super Admin (you can replace this with actual role checking logic)
  const isSuperAdmin = true; // This should come from your auth context or user data

  const handleImageUpload = () => {
    if (!isSuperAdmin) {
      toast({
        title: "Access Denied",
        description: "Only Super Admin can update profile photo.",
        variant: "destructive",
      });
      return;
    }
    setIsCropModalOpen(true);
  };

  const handleCropComplete = (croppedImageUrl: string) => {
    updateProfileImage(croppedImageUrl);
  };

  const handleUpdateProfile = () => {
    if (!isSuperAdmin) {
      toast({
        title: "Access Denied",
        description: "Only Super Admin can update profile information.",
        variant: "destructive",
      });
      return;
    }

    toast({
      title: "Profile Updated",
      description: "Your profile has been successfully updated.",
    });
  };

  const getInitials = (name: string) => {
    return name
      .split(" ")
      .map((n) => n[0])
      .join("")
      .toUpperCase();
  };

  const isSuperAdmin = userRole === "admin" || userRole === "super-admin";

  return (
    <div className="space-y-8">
      {/* Profile Section */}
      <Card className="transition-colors duration-500 ease-in-out">
        <CardContent className="pt-8">
          <div className="flex items-center gap-8 mb-8">
            <div className="relative">
              {profileData.profileImage ? (
                <img
                  src={profileData.profileImage}
                  alt="Profile"
                  className="w-32 h-32 rounded-full object-cover border-4 border-gray-200 dark:border-gray-600"
                />
              ) : (
                <div className="w-32 h-32 rounded-full bg-gradient-to-br from-orange-400 to-pink-400 flex items-center justify-center border-4 border-gray-200 dark:border-gray-600">
                  <span className="text-3xl font-bold text-white">
                    {getInitials(profileData.name)}
                  </span>
                </div>
              )}
              <button
                onClick={handleImageUpload}
                className="absolute bottom-0 right-0 bg-white dark:bg-gray-700 rounded-full p-3 shadow-lg cursor-pointer border border-gray-200 dark:border-gray-600 transition-all duration-300 ease-in-out hover:scale-105"
              >
                <Camera className="w-5 h-5 text-gray-600 dark:text-gray-300" />
              </button>
            </div>
            <div>
              <h2 className="text-3xl font-bold text-gray-900 dark:text-white">
                {profileData.name}
              </h2>
              <p className="text-xl text-gray-600 dark:text-gray-400">
                {profileData.jobTitle}
              </p>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-2">
              <Label htmlFor="name" className="text-lg">
                Name
              </Label>
              <Input
                id="name"
                value={profileData.name}
                onChange={(e) => updateProfileData({ name: e.target.value })}
                className="transition-colors duration-300 ease-in-out text-lg"
                disabled={!isSuperAdmin}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="email" className="text-lg">
                Email
              </Label>
              <Input
                id="email"
                type="email"
                value={profileData.email}
                onChange={(e) => updateProfileData({ email: e.target.value })}
                className="transition-colors duration-300 ease-in-out text-lg"
                disabled={!isSuperAdmin}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="phone" className="text-lg">
                Phone Number
              </Label>
              <Input
                id="phone"
                value={profileData.phone}
                onChange={(e) => updateProfileData({ phone: e.target.value })}
                className="transition-colors duration-300 ease-in-out text-lg"
                disabled={!isSuperAdmin}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="jobTitle" className="text-lg">
                Job Title
              </Label>
              <Input
                id="jobTitle"
                value={profileData.jobTitle}
                onChange={(e) =>
                  updateProfileData({ jobTitle: e.target.value })
                }
                className="transition-colors duration-300 ease-in-out text-lg"
                disabled={!isSuperAdmin}
              />
            </div>

            <div className="space-y-2 md:col-span-2">
              <Label htmlFor="department" className="text-lg">
                Department
              </Label>
              <Input
                id="department"
                value={profileData.department}
                onChange={(e) =>
                  updateProfileData({ department: e.target.value })
                }
                className="transition-colors duration-300 ease-in-out text-lg"
                disabled={!isSuperAdmin}
              />
            </div>
          </div>

          {!isSuperAdmin && (
            <div className="mt-4 p-4 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg">
              <p className="text-base text-yellow-800 dark:text-yellow-200">
                Profile fields are read-only. Only Super Admin can modify
                profile information.
              </p>
            </div>
          )}

          <div className="flex justify-end mt-8">
            <Button
              onClick={handleUpdateProfile}
              disabled={!isSuperAdmin}
              className="bg-green-600 hover:bg-green-700 transition-all duration-300 ease-in-out text-lg px-8 py-4 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Update Profile
            </Button>
          </div>
        </CardContent>
      </Card>

      <ImageCropModal
        isOpen={isCropModalOpen}
        onClose={() => setIsCropModalOpen(false)}
        onCropComplete={handleCropComplete}
      />
    </div>
  );
};

export default ProfileTab;
