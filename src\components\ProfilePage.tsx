import React, { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>it<PERSON> } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Camera } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import ImageCropModal from "./ImageCropModal";
import { useProfile } from "@/contexts/ProfileContext";

const ProfilePage = () => {
  const { toast } = useToast();
  const { profileData, updateProfileData, updateProfileImage, getInitials } =
    useProfile();
  const [isCropModalOpen, setIsCropModalOpen] = useState(false);

  const handleImageUpload = () => {
    setIsCropModalOpen(true);
  };

  const handleCropComplete = (croppedImageUrl: string) => {
    updateProfileImage(croppedImageUrl);
  };

  const handleUpdateProfile = () => {
    toast({
      title: "Profile Updated",
      description: "Your profile has been successfully updated.",
    });
  };

  return (
    <div className="space-y-8">
      <div>
        <h1 className="text-3xl font-bold text-gray-900 dark:text-white transition-colors duration-500 ease-in-out">
          Profile
        </h1>
        <p className="text-base text-gray-600 dark:text-gray-300 transition-colors duration-500 ease-in-out">
          Manage your profile information
        </p>
      </div>

      {/* Profile Section */}
      <Card className="transition-colors duration-500 ease-in-out bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700">
        <CardHeader>
          <CardTitle className="text-2xl transition-colors duration-500 ease-in-out text-gray-900 dark:text-white">
            Profile Information
          </CardTitle>
        </CardHeader>
        <CardContent className="pt-8">
          <div className="flex items-center gap-8 mb-8">
            <div className="relative">
              {profileData.profileImage ? (
                <img
                  src={profileData.profileImage}
                  alt="Profile"
                  className="w-32 h-32 rounded-full object-cover border-4 border-gray-200 dark:border-gray-600"
                />
              ) : (
                <div className="w-32 h-32 rounded-full bg-gradient-to-br from-orange-400 to-pink-400 flex items-center justify-center border-4 border-gray-200 dark:border-gray-600">
                  <span className="text-3xl font-bold text-white">
                    {getInitials(profileData.name)}
                  </span>
                </div>
              )}
              <button
                onClick={handleImageUpload}
                className="absolute bottom-0 right-0 bg-white dark:bg-gray-700 rounded-full p-3 shadow-lg cursor-pointer border border-gray-200 dark:border-gray-600 transition-all duration-300 ease-in-out hover:scale-105"
              >
                <Camera className="w-5 h-5 text-gray-600 dark:text-gray-300" />
              </button>
            </div>
            <div>
              <h2 className="text-3xl font-bold text-gray-900 dark:text-white">
                {profileData.name}
              </h2>
              <p className="text-xl text-gray-600 dark:text-white">
                {profileData.jobTitle}
              </p>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-2">
              <Label
                htmlFor="name"
                className="text-lg font-medium transition-colors duration-500 ease-in-out text-gray-700 dark:text-gray-300"
              >
                Name
              </Label>
              <Input
                id="name"
                value={profileData.name}
                onChange={(e) => updateProfileData({ name: e.target.value })}
                className="transition-colors duration-500 ease-in-out bg-white dark:bg-gray-700 border-gray-300 dark:border-gray-600 text-base"
              />
            </div>

            <div className="space-y-2">
              <Label
                htmlFor="email"
                className="text-lg font-medium transition-colors duration-500 ease-in-out text-gray-700 dark:text-gray-300"
              >
                Email
              </Label>
              <Input
                id="email"
                type="email"
                value={profileData.email}
                onChange={(e) => updateProfileData({ email: e.target.value })}
                className="transition-colors duration-500 ease-in-out bg-white dark:bg-gray-700 border-gray-300 dark:border-gray-600 text-base"
              />
            </div>

            <div className="space-y-2">
              <Label
                htmlFor="phone"
                className="text-lg font-medium transition-colors duration-500 ease-in-out text-gray-700 dark:text-gray-300"
              >
                Phone Number
              </Label>
              <Input
                id="phone"
                value={profileData.phone}
                onChange={(e) => updateProfileData({ phone: e.target.value })}
                className="transition-colors duration-500 ease-in-out bg-white dark:bg-gray-700 border-gray-300 dark:border-gray-600 text-base"
              />
            </div>

            <div className="space-y-2">
              <Label
                htmlFor="jobTitle"
                className="text-lg font-medium transition-colors duration-500 ease-in-out text-gray-700 dark:text-gray-300"
              >
                Job Title
              </Label>
              <Input
                id="jobTitle"
                value={profileData.jobTitle}
                onChange={(e) =>
                  updateProfileData({ jobTitle: e.target.value })
                }
                className="transition-colors duration-500 ease-in-out bg-white dark:bg-gray-700 border-gray-300 dark:border-gray-600 text-base"
              />
            </div>

            <div className="space-y-2 md:col-span-2">
              <Label
                htmlFor="department"
                className="text-lg font-medium transition-colors duration-500 ease-in-out text-gray-700 dark:text-gray-300"
              >
                Department
              </Label>
              <Input
                id="department"
                value={profileData.department}
                onChange={(e) =>
                  updateProfileData({ department: e.target.value })
                }
                className="transition-colors duration-500 ease-in-out bg-white dark:bg-gray-700 border-gray-300 dark:border-gray-600 text-base"
              />
            </div>
          </div>
        </CardContent>
      </Card>

      <ImageCropModal
        isOpen={isCropModalOpen}
        onClose={() => setIsCropModalOpen(false)}
        onCropComplete={handleCropComplete}
      />
    </div>
  );
};

export default ProfilePage;
