import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { useToast } from "@/hooks/use-toast";
import { Plus, Edit, Trash2 } from "lucide-react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import AnimatedNumber from "./AnimatedNumber";
import AnimatedText from "./AnimatedText";
import AddStaffModal from "./AddStaffModal";
import EditStaffModal from "./EditStaffModal";

const staffStats = [
  { title: "Total Staff", value: "6", color: "text-gray-900" },
  { title: "Active", value: "5", color: "text-green-600" },
  { title: "Inactive", value: "1", color: "text-red-600" },
  { title: "Departments", value: "4", color: "text-blue-600" },
];

const initialStaffData = [
  {
    id: 1,
    name: "Amit Sharma",
    email: "<EMAIL>",
    phone: "+91 9876543210",
    position: "Operations Manager",
    department: "Operations",
    joinDate: "2023-01-15",
    status: "Active",
  },
  {
    id: 2,
    name: "Priya Patel",
    email: "<EMAIL>",
    phone: "+91 9876543211",
    position: "Customer Service Lead",
    department: "Customer Service",
    joinDate: "2023-03-20",
    status: "Active",
  },
  {
    id: 3,
    name: "Rahul Singh",
    email: "<EMAIL>",
    phone: "+91 9876543212",
    position: "Marketing Executive",
    department: "Marketing",
    joinDate: "2023-05-10",
    status: "Active",
  },
  {
    id: 4,
    name: "Sneha Gupta",
    email: "<EMAIL>",
    phone: "+91 9876543213",
    position: "Quality Analyst",
    department: "Quality",
    joinDate: "2023-07-05",
    status: "Active",
  },
  {
    id: 5,
    name: "Vikram Reddy",
    email: "<EMAIL>",
    phone: "+91 9876543214",
    position: "Inventory Coordinator",
    department: "Operations",
    joinDate: "2023-09-12",
    status: "Active",
  },
  {
    id: 6,
    name: "Anjali Mehta",
    email: "<EMAIL>",
    phone: "+91 9876543215",
    position: "Sales Associate",
    department: "Sales",
    joinDate: "2023-11-01",
    status: "Inactive",
  },
];

const StaffManagement = () => {
  const { toast } = useToast();
  const [staff, setStaff] = useState(initialStaffData);
  const [isAddModalOpen, setIsAddModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [selectedStaff, setSelectedStaff] = useState(null);
  // For role-based access control - in a real app, this would come from auth context
  const [userRole] = useState("Admin"); // Can be "Admin", "Super Admin", "Manager", etc.

  const handleStatusChange = (id: number, newStatus: string) => {
    setStaff((prev) =>
      prev.map((member) =>
        member.id === id ? { ...member, status: newStatus } : member
      )
    );

    toast({
      title: "Status Updated",
      description: `Staff member status changed to ${newStatus}`,
    });
  };

  const handleStatusToggle = (id: number, currentStatus: string) => {
    const statusCycle = ["Active", "Inactive"];
    const currentIndex = statusCycle.indexOf(currentStatus);
    const nextStatus = statusCycle[(currentIndex + 1) % statusCycle.length];
    handleStatusChange(id, nextStatus);
  };

  const handleAddStaff = (newStaff: any) => {
    setStaff((prev) => [...prev, newStaff]);
  };

  const handleEditStaff = (staffMember: any) => {
    setSelectedStaff(staffMember);
    setIsEditModalOpen(true);
  };

  const handleUpdateStaff = (updatedStaff: any) => {
    setStaff((prev) =>
      prev.map((member) =>
        member.id === updatedStaff.id ? updatedStaff : member
      )
    );
    setIsEditModalOpen(false);
    setSelectedStaff(null);
  };

  const handleDeleteStaff = (id: number, name: string) => {
    setStaff((prev) => prev.filter((member) => member.id !== id));
    toast({
      title: "Staff Deleted",
      description: `${name} has been removed from staff`,
    });
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "Active":
        return "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200";
      case "Inactive":
        return "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200";
      default:
        return "bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200";
    }
  };

  // Update stats based on current staff
  const activeStaff = staff.filter(
    (member) => member.status === "Active"
  ).length;
  const inactiveStaff = staff.filter(
    (member) => member.status === "Inactive"
  ).length;
  const departments = new Set(staff.map((member) => member.department)).size;

  const updatedStats = [
    {
      title: "Total Staff",
      value: staff.length.toString(),
      color: "text-gray-900",
    },
    { title: "Active", value: activeStaff.toString(), color: "text-green-600" },
    {
      title: "Inactive",
      value: inactiveStaff.toString(),
      color: "text-red-600",
    },
    {
      title: "Departments",
      value: departments.toString(),
      color: "text-blue-600",
    },
  ];

  return (
    <div className="space-y-6 transition-colors duration-500 ease-in-out">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white transition-colors duration-500 ease-in-out">
            Staff Management
          </h1>
          <p className="text-base text-gray-600 dark:text-gray-300 transition-colors duration-500 ease-in-out">
            Manage team members and their access permissions
          </p>
        </div>
        <Button
          onClick={() => setIsAddModalOpen(true)}
          className="bg-green-600 hover:bg-green-700 text-white"
        >
          <Plus className="w-4 h-4 mr-2" />
          Add Staff
        </Button>
      </div>

      {/* Staff Statistics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {updatedStats.map((stat, index) => (
          <Card
            key={index}
            className="transition-colors duration-500 ease-in-out bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700"
          >
            <CardContent className="p-6">
              <div className="space-y-2">
                <p className="text-lg font-medium text-gray-600 dark:text-gray-400 transition-colors duration-500 ease-in-out">
                  {stat.title}
                </p>
                <AnimatedNumber
                  value={stat.value}
                  className="text-3xl font-bold"
                />
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Staff Table */}
      <Card className="transition-colors duration-500 ease-in-out bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700">
        <CardHeader>
          <CardTitle className="text-gray-900 dark:text-white transition-colors duration-500 ease-in-out">
            Team Members
          </CardTitle>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead className="text-lg font-medium transition-colors duration-500 ease-in-out text-gray-700 dark:text-gray-300 text-center">
                  Staff Details
                </TableHead>
                <TableHead className="text-lg font-medium transition-colors duration-500 ease-in-out text-gray-700 dark:text-gray-300 text-center">
                  Position
                </TableHead>
                <TableHead className="text-lg font-medium transition-colors duration-500 ease-in-out text-gray-700 dark:text-gray-300 text-center">
                  Department
                </TableHead>
                <TableHead className="text-lg font-medium transition-colors duration-500 ease-in-out text-gray-700 dark:text-gray-300 text-center">
                  Join Date
                </TableHead>
                <TableHead className="text-lg font-medium transition-colors duration-500 ease-in-out text-gray-700 dark:text-gray-300 text-center">
                  Status
                </TableHead>
                <TableHead className="text-lg font-medium transition-colors duration-500 ease-in-out text-gray-700 dark:text-gray-300 text-center">
                  Actions
                </TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {staff.map((member) => (
                <TableRow key={member.id}>
                  <TableCell className="text-center">
                    <div className="flex items-center justify-center gap-3">
                      <div className="w-10 h-10 bg-blue-600 rounded-full flex items-center justify-center text-white font-medium">
                        {member.name
                          .split(" ")
                          .map((n) => n[0])
                          .join("")}
                      </div>
                      <div className="flex flex-col">
                        <AnimatedText className="font-medium text-lg">
                          {member.name}
                        </AnimatedText>
                        <span className="text-base text-gray-500 dark:text-gray-400 transition-colors duration-500 ease-in-out">
                          {member.email}
                        </span>
                        <span className="text-base text-gray-500 dark:text-gray-400 transition-colors duration-500 ease-in-out">
                          {member.phone}
                        </span>
                      </div>
                    </div>
                  </TableCell>
                  <TableCell className="text-center">
                    <span className="font-medium text-lg text-gray-900 dark:text-white transition-colors duration-500 ease-in-out">
                      {member.position}
                    </span>
                  </TableCell>
                  <TableCell className="text-center">
                    <Badge variant="outline" className="text-base">
                      {member.department}
                    </Badge>
                  </TableCell>
                  <TableCell className="text-center text-lg text-gray-600 dark:text-gray-400 transition-colors duration-500 ease-in-out">
                    {member.joinDate}
                  </TableCell>
                  <TableCell className="text-center">
                    <Button
                      variant="outline"
                      onClick={() =>
                        handleStatusToggle(member.id, member.status)
                      }
                      className="w-full max-w-[140px] text-base bg-red-50 hover:bg-red-100 border-red-200 text-red-700 dark:bg-black dark:hover:bg-gray-800 dark:border-gray-600 dark:text-white"
                    >
                      <Badge
                        className={`${getStatusColor(member.status)} text-base`}
                      >
                        {member.status}
                      </Badge>
                    </Button>
                  </TableCell>
                  <TableCell className="text-center">
                    <div className="flex items-center justify-center gap-2">
                      {/* Edit button restricted to Admin/Super Admin roles only */}
                      {(userRole === "Admin" || userRole === "Super Admin") && (
                        <Button
                          variant="ghost"
                          size="sm"
                          className="p-2 hover:bg-blue-50 dark:hover:bg-blue-900"
                          onClick={() => handleEditStaff(member)}
                        >
                          <Edit className="w-4 h-4 text-blue-600 dark:text-blue-400" />
                        </Button>
                      )}
                      {/* Delete button restricted to Admin/Super Admin roles only */}
                      {(userRole === "Admin" || userRole === "Super Admin") && (
                        <Button
                          variant="ghost"
                          size="sm"
                          className="p-2 hover:bg-red-50 dark:hover:bg-red-900"
                          onClick={() => {
                            handleDeleteStaff(member.id, member.name);
                          }}
                        >
                          <Trash2 className="w-4 h-4 text-red-600 dark:text-red-400" />
                        </Button>
                      )}
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>

      <AddStaffModal
        isOpen={isAddModalOpen}
        onClose={() => setIsAddModalOpen(false)}
        onAdd={handleAddStaff}
      />

      <EditStaffModal
        isOpen={isEditModalOpen}
        onClose={() => {
          setIsEditModalOpen(false);
          setSelectedStaff(null);
        }}
        staff={selectedStaff}
        onUpdate={handleUpdateStaff}
      />
    </div>
  );
};

export default StaffManagement;
