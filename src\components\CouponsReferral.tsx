import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { Plus, Edit, Trash2, Co<PERSON>, <PERSON>rkles } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import CreateCouponModal from "./CreateCouponModal";
import CreateDiscountModal from "./CreateDiscountModal";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import AnimatedNumber from "./AnimatedNumber";
import AnimatedText from "./AnimatedText";
import { usePagination } from "@/hooks/use-pagination";
import { TablePagination } from "@/components/ui/table-pagination";

const couponStats = [
  { title: "Total Coupons", value: "3", color: "text-gray-900" },
  { title: "Active", value: "2", color: "text-[#FFD700]" },
  { title: "Inactive", value: "1", color: "text-red-600" },
  { title: "Total Savings", value: "₹12,450", color: "text-blue-600" },
];

const couponsData = [
  {
    id: 1,
    code: "WELCOME10",
    description: "Welcome discount for new customers",
    type: "Percentage",
    value: 10,
    minOrder: 500,
    maxDiscount: 100,
    usageLimit: 1000,
    used: 45,
    status: "Active",
    validFrom: "2024-01-01",
    validTo: "2024-12-31",
    productName: "Kapiva Aloe Vera Juice",
    category: "Health Drinks",
  },
  {
    id: 2,
    code: "HEALTH50",
    description: "Flat discount on health products",
    type: "Fixed",
    value: 50,
    minOrder: 300,
    maxDiscount: 50,
    usageLimit: 500,
    used: 123,
    status: "Active",
    validFrom: "2024-01-15",
    validTo: "2024-06-30",
    productName: "Ashwagandha Capsules",
    category: "Supplements",
  },
  {
    id: 3,
    code: "SUMMER25",
    description: "Summer special discount",
    type: "Percentage",
    value: 25,
    minOrder: 800,
    maxDiscount: 200,
    usageLimit: 300,
    used: 87,
    status: "Active",
    validFrom: "2024-06-01",
    validTo: "2024-08-31",
    productName: "Vitamin D3 Tablets",
    category: "Vitamins",
  },
  {
    id: 4,
    code: "NEWUSER",
    description: "First time user discount",
    type: "Fixed",
    value: 100,
    minOrder: 600,
    maxDiscount: 100,
    usageLimit: 1000,
    used: 234,
    status: "Active",
    validFrom: "2024-01-01",
    validTo: "2024-12-31",
    productName: "Omega 3 Capsules",
    category: "Supplements",
  },
  {
    id: 5,
    code: "MONSOON15",
    description: "Monsoon health care discount",
    type: "Percentage",
    value: 15,
    minOrder: 400,
    maxDiscount: 150,
    usageLimit: 750,
    used: 156,
    status: "Active",
    validFrom: "2024-07-01",
    validTo: "2024-09-30",
    productName: "Immunity Booster",
    category: "Health Drinks",
  },
  {
    id: 6,
    code: "BULK20",
    description: "Bulk purchase discount",
    type: "Percentage",
    value: 20,
    minOrder: 1000,
    maxDiscount: 300,
    usageLimit: 200,
    used: 45,
    status: "Active",
    validFrom: "2024-03-01",
    validTo: "2024-12-31",
    productName: "Protein Powder",
    category: "Supplements",
  },
  {
    id: 7,
    code: "STUDENT10",
    description: "Student discount offer",
    type: "Percentage",
    value: 10,
    minOrder: 250,
    maxDiscount: 75,
    usageLimit: 500,
    used: 189,
    status: "Active",
    validFrom: "2024-01-01",
    validTo: "2024-12-31",
    productName: "Multivitamin Tablets",
    category: "Vitamins",
  },
  {
    id: 8,
    code: "SENIOR20",
    description: "Senior citizen discount",
    type: "Percentage",
    value: 20,
    minOrder: 300,
    maxDiscount: 100,
    usageLimit: 400,
    used: 67,
    status: "Active",
    validFrom: "2024-01-01",
    validTo: "2024-12-31",
    productName: "Calcium Tablets",
    category: "Supplements",
  },
  {
    id: 9,
    code: "FESTIVE30",
    description: "Festival special offer",
    type: "Percentage",
    value: 30,
    minOrder: 1200,
    maxDiscount: 400,
    usageLimit: 150,
    used: 23,
    status: "Inactive",
    validFrom: "2024-10-01",
    validTo: "2024-11-15",
    productName: "Herbal Tea",
    category: "Health Drinks",
  },
  {
    id: 10,
    code: "LOYALTY15",
    description: "Loyalty program discount",
    type: "Percentage",
    value: 15,
    minOrder: 500,
    maxDiscount: 125,
    usageLimit: 600,
    used: 298,
    status: "Active",
    validFrom: "2024-01-01",
    validTo: "2024-12-31",
    productName: "Probiotic Capsules",
    category: "Supplements",
  },
  {
    id: 11,
    code: "WEEKEND25",
    description: "Weekend special discount",
    type: "Fixed",
    value: 75,
    minOrder: 350,
    maxDiscount: 75,
    usageLimit: 300,
    used: 112,
    status: "Active",
    validFrom: "2024-01-01",
    validTo: "2024-12-31",
    productName: "Green Tea Extract",
    category: "Health Drinks",
  },
  {
    id: 12,
    code: "FAMILY40",
    description: "Family pack discount",
    type: "Percentage",
    value: 40,
    minOrder: 1500,
    maxDiscount: 500,
    usageLimit: 100,
    used: 34,
    status: "Active",
    validFrom: "2024-01-01",
    validTo: "2024-12-31",
    productName: "Family Health Kit",
    category: "Health Kits",
  },
  {
    id: 3,
    code: "EXPIRED20",
    description: "Expired discount code",
    type: "Percentage",
    value: 20,
    minOrder: 1000,
    maxDiscount: 200,
    usageLimit: 100,
    used: 89,
    status: "Inactive",
    validFrom: "2023-01-01",
    validTo: "2023-12-31",
    productName: "Health Combo Pack",
    category: "Combo Packs",
  },
];

const CouponsReferral = () => {
  const { toast } = useToast();
  const [coupons, setCoupons] = useState(couponsData);
  const [selectedCoupon, setSelectedCoupon] = useState(null);
  const [isCouponModalOpen, setIsCouponModalOpen] = useState(false);
  const [isDiscountModalOpen, setIsDiscountModalOpen] = useState(false);
  const [activeTab, setActiveTab] = useState("coupons");
  const [searchTerm, setSearchTerm] = useState("");

  const handleCreateCoupon = () => {
    setSelectedCoupon(null);
    setIsCouponModalOpen(true);
  };

  const handleCreateDiscount = () => {
    setSelectedCoupon(null);
    setIsDiscountModalOpen(true);
  };

  const handleEditCoupon = (coupon) => {
    setSelectedCoupon(coupon);
    setIsCouponModalOpen(true);
  };

  const handleDeleteCoupon = (couponId) => {
    setCoupons((prev) => prev.filter((c) => c.id !== couponId));
    toast({
      title: "Coupon Deleted",
      description: "Coupon has been successfully deleted",
    });
  };

  const handleStatusToggle = (couponId, currentStatus) => {
    const nextStatus = currentStatus === "Active" ? "Inactive" : "Active";
    setCoupons((prev) =>
      prev.map((coupon) =>
        coupon.id === couponId ? { ...coupon, status: nextStatus } : coupon
      )
    );
    toast({
      title: "Status Updated",
      description: "Coupon status has been successfully updated",
    });
  };

  const getStatusBadgeColor = (status) => {
    switch (status) {
      case "Active":
        return "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200";
      case "Inactive":
        return "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200";
      default:
        return "bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200";
    }
  };

  const handleDuplicateCoupon = (coupon) => {
    const duplicatedCoupon = {
      ...coupon,
      id: Date.now(),
      code: `${coupon.code}_COPY`,
      used: 0,
    };
    setCoupons((prev) => [...prev, duplicatedCoupon]);
    toast({
      title: "Coupon Duplicated",
      description: "Coupon has been successfully duplicated",
    });
  };

  const handleSaveCoupon = (couponData) => {
    if (selectedCoupon) {
      // Update existing coupon
      setCoupons((prev) =>
        prev.map((c) =>
          c.id === selectedCoupon.id
            ? { ...couponData, id: selectedCoupon.id }
            : c
        )
      );
      toast({
        title: "Coupon Updated",
        description: "Coupon has been successfully updated",
      });
    } else {
      // Add new coupon
      setCoupons((prev) => [
        ...prev,
        { ...couponData, id: Date.now(), used: 0 },
      ]);
      toast({
        title: "Coupon Created",
        description: "New coupon has been successfully created",
      });
    }
  };

  // Calculate dynamic stats
  const dynamicStats = [
    {
      title: "Total Coupons",
      value: coupons.length.toString(),
      color: "text-gray-900",
    },
    {
      title: "Active",
      value: coupons.filter((c) => c.status === "Active").length.toString(),
      color: "text-[#FFD700]",
    },
    {
      title: "Inactive",
      value: coupons.filter((c) => c.status === "Inactive").length.toString(),
      color: "text-red-600",
    },
    {
      title: "Total Savings",
      value: `₹${coupons
        .reduce((sum, c) => sum + c.used * c.value, 0)
        .toLocaleString()}`,
      color: "text-blue-600",
    },
  ];

  // Filter coupons based on search
  const filteredCoupons = coupons.filter(
    (coupon) =>
      coupon.code.toLowerCase().includes(searchTerm.toLowerCase()) ||
      coupon.productName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      coupon.category.toLowerCase().includes(searchTerm.toLowerCase())
  );

  // Pagination logic for coupons
  const {
    currentPage,
    totalPages,
    paginatedData: paginatedCoupons,
    goToPage,
    totalItems,
  } = usePagination({
    data: filteredCoupons,
    itemsPerPage: 10,
  });

  return (
    <div className="space-y-6 transition-colors duration-500 ease-in-out">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white transition-colors duration-500 ease-in-out">
            Coupons
          </h1>
          <p className="text-base text-gray-600 dark:text-gray-300 transition-colors duration-500 ease-in-out">
            Manage discount coupons and referral programs
          </p>
        </div>
      </div>

      {/* Dynamic Coupon Statistics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {dynamicStats.map((stat, index) => (
          <Card
            key={index}
            className="transition-colors duration-500 ease-in-out bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700"
          >
            <CardContent className="p-6">
              <div className="space-y-2">
                <p className="text-lg font-medium text-gray-600 dark:text-gray-400 transition-colors duration-500 ease-in-out">
                  {stat.title}
                </p>
                <AnimatedNumber
                  value={stat.value}
                  className={`text-3xl font-bold ${stat.color}`}
                />
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Search and Filter */}
      <Card className="transition-colors duration-500 ease-in-out bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700">
        <CardContent className="p-6">
          <div className="flex items-center justify-between gap-4">
            <div className="flex-1">
              <Input
                placeholder="Search coupons by code, product, or category..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="transition-colors duration-500 ease-in-out bg-white dark:bg-gray-700 border-gray-300 dark:border-gray-600 text-base"
              />
            </div>
            <div className="flex gap-2">
              <Button
                className="bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 transition-colors duration-300 ease-in-out px-6 py-3 text-base border border-gray-300 dark:border-gray-600"
                onClick={handleCreateCoupon}
              >
                <Sparkles className="w-5 h-5 mr-2" />
                Create Coupon
              </Button>
              <Button
                className="bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 transition-colors duration-300 ease-in-out px-6 py-3 text-base border border-gray-300 dark:border-gray-600"
                onClick={handleCreateDiscount}
              >
                <Plus className="w-5 h-5 mr-2" />
                Create Discount
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Enhanced Tabs for Create Coupon and Create Discount */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <div className="flex items-center justify-between mb-4">
          <div className="border border-gray-300 dark:border-gray-600 rounded-lg p-1 bg-white dark:bg-gray-700 transition-colors duration-500 ease-in-out">
            <TabsList className="grid w-full max-w-md grid-cols-2 bg-transparent">
              <TabsTrigger
                value="coupons"
                className="text-base text-gray-700 dark:text-gray-300 transition-colors duration-500 ease-in-out"
              >
                Coupons
              </TabsTrigger>
              <TabsTrigger
                value="discounts"
                className="text-base text-gray-700 dark:text-gray-300 transition-colors duration-500 ease-in-out"
              >
                Discounts
              </TabsTrigger>
            </TabsList>
          </div>
        </div>

        <TabsContent value="coupons">
          <Card className="transition-colors duration-500 ease-in-out bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700">
            <CardHeader>
              <CardTitle className="text-2xl text-gray-900 dark:text-white transition-colors duration-500 ease-in-out">
                Coupons
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {/* Table Header */}
                <div className="grid grid-cols-7 gap-4 p-4 bg-gray-50 dark:bg-gray-700 rounded-lg font-medium transition-colors duration-500 ease-in-out text-gray-700 dark:text-gray-300 text-lg border-b border-gray-200 dark:border-gray-600">
                  <div className="text-center">Coupon Code</div>
                  <div className="text-center">Discount Type & Value</div>
                  <div className="text-center">Status</div>
                  <div className="text-center">Valid Period</div>
                  <div className="text-center">Product Name</div>
                  <div className="text-center">Category</div>
                  <div className="text-center">Actions</div>
                </div>
                {/* Table Rows */}
                {paginatedCoupons.map((coupon) => (
                  <div
                    key={coupon.id}
                    className="grid grid-cols-7 gap-4 p-4 border-b border-gray-200 dark:border-gray-600 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors duration-300 ease-in-out"
                  >
                    {/* Coupon Code */}
                    <div className="text-center">
                      <div className="flex flex-col items-center">
                        <AnimatedText className="font-medium text-lg">
                          {coupon.code}
                        </AnimatedText>
                        <span className="text-base text-gray-500 dark:text-gray-400 transition-colors duration-500 ease-in-out">
                          {coupon.description}
                        </span>
                      </div>
                    </div>

                    {/* Discount Type & Value */}
                    <div className="flex flex-col items-center justify-center">
                      <Badge variant="outline" className="w-fit mb-1">
                        {coupon.type}
                      </Badge>
                      <span className="text-lg font-medium text-gray-900 dark:text-white transition-colors duration-500 ease-in-out">
                        {coupon.type === "Percentage"
                          ? `${coupon.value}%`
                          : `₹${coupon.value}`}
                      </span>
                    </div>

                    {/* Status */}
                    <div className="flex justify-center">
                      <Button
                        variant="outline"
                        onClick={() =>
                          handleStatusToggle(coupon.id, coupon.status)
                        }
                        className="w-full max-w-[140px] text-base bg-red-50 hover:bg-red-100 border-red-200 text-red-700 dark:bg-black dark:hover:bg-gray-800 dark:border-gray-600 dark:text-white"
                      >
                        <Badge
                          className={`${getStatusBadgeColor(
                            coupon.status
                          )} text-base`}
                        >
                          {coupon.status}
                        </Badge>
                      </Button>
                    </div>

                    {/* Valid Period */}
                    <div className="text-center">
                      <div className="text-base text-gray-900 dark:text-white transition-colors duration-500 ease-in-out">
                        <div>{coupon.validFrom}</div>
                        <div className="text-base text-gray-500 dark:text-gray-400 transition-colors duration-500 ease-in-out">
                          to {coupon.validTo}
                        </div>
                      </div>
                    </div>

                    {/* Product Name */}
                    <div className="text-center">
                      <span className="font-medium text-lg text-gray-900 dark:text-white transition-colors duration-500 ease-in-out">
                        {coupon.productName}
                      </span>
                    </div>

                    {/* Category */}
                    <div className="flex justify-center">
                      <Badge
                        variant="outline"
                        className="w-fit px-3 py-1 text-base font-medium border-gray-300 dark:border-gray-600 bg-gray-50 dark:bg-gray-700 text-gray-700 dark:text-gray-300"
                      >
                        {coupon.category}
                      </Badge>
                    </div>

                    {/* Actions */}
                    <div className="flex items-center justify-center gap-2">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleEditCoupon(coupon)}
                        className="transition-colors duration-300 ease-in-out p-2"
                      >
                        <Edit className="w-4 h-4" />
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleDuplicateCoupon(coupon)}
                        className="transition-colors duration-300 ease-in-out p-2"
                      >
                        <Copy className="w-4 h-4" />
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleDeleteCoupon(coupon.id)}
                        className="transition-colors duration-300 ease-in-out text-red-600 hover:text-red-700 p-2"
                      >
                        <Trash2 className="w-4 h-4" />
                      </Button>
                    </div>
                  </div>
                ))}

                {/* Pagination */}
                <TablePagination
                  currentPage={currentPage}
                  totalPages={totalPages}
                  onPageChange={goToPage}
                  totalItems={totalItems}
                  itemsPerPage={10}
                />
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="discounts">
          <Card className="transition-colors duration-500 ease-in-out bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700">
            <CardHeader>
              <CardTitle className="text-2xl text-gray-900 dark:text-white transition-colors duration-500 ease-in-out">
                Active Discounts
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-center py-8">
                <p className="text-base text-gray-500 dark:text-gray-400">
                  No discounts available yet.
                </p>
                <Button
                  className="mt-4 bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 transition-colors duration-300 ease-in-out px-6 py-3 text-base border border-gray-300 dark:border-gray-600"
                  onClick={handleCreateDiscount}
                >
                  Create Your First Discount
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      <CreateCouponModal
        isOpen={isCouponModalOpen}
        onClose={() => setIsCouponModalOpen(false)}
        coupon={selectedCoupon}
        onSave={handleSaveCoupon}
      />

      <CreateDiscountModal
        isOpen={isDiscountModalOpen}
        onClose={() => setIsDiscountModalOpen(false)}
        onSave={handleSaveCoupon}
      />
    </div>
  );
};

export default CouponsReferral;
