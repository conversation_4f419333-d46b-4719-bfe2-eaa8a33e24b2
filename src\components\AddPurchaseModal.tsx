import { useState } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>eader,
  <PERSON>alogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { toast } from "@/hooks/use-toast";

interface InventoryItem {
  product: string;
  currentStock: number;
  stockLevel: string;
  value: string;
  lastRestocked: string;
  status: string;
}

interface AddPurchaseModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (data: InventoryItem) => void;
}

const AddPurchaseModal = ({
  isOpen,
  onClose,
  onSubmit,
}: AddPurchaseModalProps) => {
  const [formData, setFormData] = useState<InventoryItem>({
    product: "",
    currentStock: 0,
    stockLevel: "",
    value: "",
    lastRestocked: "",
    status: "",
  });

  const handleInputChange = (
    field: keyof InventoryItem,
    value: string | number
  ) => {
    setFormData((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    if (
      !formData.product ||
      !formData.currentStock ||
      !formData.stockLevel ||
      !formData.value ||
      !formData.lastRestocked ||
      !formData.status
    ) {
      toast({
        title: "Validation Error",
        description: "Please fill in all required fields",
        variant: "destructive",
      });
      return;
    }

    onSubmit(formData);
    onClose();

    // Reset form
    setFormData({
      product: "",
      currentStock: 0,
      stockLevel: "",
      value: "",
      lastRestocked: "",
      status: "",
    });
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Add Inventory Item</DialogTitle>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="product">Product *</Label>
              <Select
                value={formData.product}
                onValueChange={(value) => handleInputChange("product", value)}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select product" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="Kapiva Aloe Vera Juice">
                    Kapiva Aloe Vera Juice
                  </SelectItem>
                  <SelectItem value="Ashwagandha Capsules">
                    Ashwagandha Capsules
                  </SelectItem>
                  <SelectItem value="Triphala Churna">
                    Triphala Churna
                  </SelectItem>
                  <SelectItem value="Giloy Juice">Giloy Juice</SelectItem>
                  <SelectItem value="Brahmi Capsules">
                    Brahmi Capsules
                  </SelectItem>
                  <SelectItem value="Turmeric Powder">
                    Turmeric Powder
                  </SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="currentStock">Current Stock *</Label>
              <Input
                id="currentStock"
                type="number"
                value={formData.currentStock || ""}
                onChange={(e) =>
                  handleInputChange("currentStock", Number(e.target.value))
                }
                placeholder="Enter current stock"
                min="0"
                required
              />
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="stockLevel">Stock Level *</Label>
              <Select
                value={formData.stockLevel}
                onValueChange={(value) =>
                  handleInputChange("stockLevel", value)
                }
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select stock level" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="High">High</SelectItem>
                  <SelectItem value="Medium">Medium</SelectItem>
                  <SelectItem value="Low">Low</SelectItem>
                  <SelectItem value="Critical">Critical</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="value">Value *</Label>
              <Input
                id="value"
                type="text"
                value={formData.value}
                onChange={(e) => handleInputChange("value", e.target.value)}
                placeholder="e.g., ₹15,000"
                required
              />
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="lastRestocked">Last Restocked *</Label>
              <Input
                id="lastRestocked"
                type="date"
                value={formData.lastRestocked}
                onChange={(e) =>
                  handleInputChange("lastRestocked", e.target.value)
                }
                required
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="status">Status *</Label>
              <Select
                value={formData.status}
                onValueChange={(value) => handleInputChange("status", value)}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="in stock">In Stock</SelectItem>
                  <SelectItem value="low stock">Low Stock</SelectItem>
                  <SelectItem value="out of stock">Out of Stock</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="flex justify-end gap-3 pt-4">
            <Button type="button" variant="outline" onClick={onClose}>
              Cancel
            </Button>
            <Button type="submit" className="bg-green-600 hover:bg-green-700">
              Add to Inventory
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
};

export default AddPurchaseModal;
