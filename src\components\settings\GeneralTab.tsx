
import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>eader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Checkbox } from "@/components/ui/checkbox";
import { Switch } from "@/components/ui/switch";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { useToast } from "@/hooks/use-toast";

const GeneralTab = () => {
  const { toast } = useToast();
  const [userRole] = useState('admin'); // This would come from auth context
  const [settings, setSettings] = useState({
    defaultDepartment: '',
    emailNotifications: true,
    smsNotifications: false,
    inAppNotifications: true,
    defaultRole: '',
    exportFormat: '',
    autoAssignOrders: false,
    enableMarketingBanners: true
  });

  const handleSaveChanges = () => {
    toast({
      title: "Settings Saved",
      description: "Your preferences have been updated successfully.",
    });
  };

  const isSuperAdmin = userRole === 'admin' || userRole === 'super-admin';

  return (
    <div className="space-y-8">
      <Card className="transition-colors duration-500 ease-in-out">
        <CardHeader>
          <CardTitle className="text-2xl">General Settings</CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="space-y-2">
            <Label htmlFor="department" className="text-lg">Default Department</Label>
            <Select 
              value={settings.defaultDepartment} 
              onValueChange={(value) => setSettings({...settings, defaultDepartment: value})}
              disabled={!isSuperAdmin}
            >
              <SelectTrigger className="text-lg">
                <SelectValue placeholder="Select a department" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="operations">Operations</SelectItem>
                <SelectItem value="marketing">Marketing</SelectItem>
                <SelectItem value="sales">Sales</SelectItem>
                <SelectItem value="support">Customer Support</SelectItem>
              </SelectContent>
            </Select>
            {!isSuperAdmin && (
              <p className="text-sm text-gray-500 dark:text-gray-400">Only Super Admin can modify this setting</p>
            )}
          </div>
        </CardContent>
      </Card>

      <Card className="transition-colors duration-500 ease-in-out">
        <CardHeader>
          <CardTitle className="text-2xl">Notification Preferences</CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="flex items-center space-x-3">
            <Checkbox 
              id="email-notifications"
              checked={settings.emailNotifications}
              onCheckedChange={(checked) => setSettings({...settings, emailNotifications: checked as boolean})}
            />
            <Label htmlFor="email-notifications" className="text-lg font-normal">
              Email alerts for new orders
            </Label>
          </div>
          
          <div className="flex items-center space-x-3">
            <Checkbox 
              id="sms-notifications"
              checked={settings.smsNotifications}
              onCheckedChange={(checked) => setSettings({...settings, smsNotifications: checked as boolean})}
            />
            <Label htmlFor="sms-notifications" className="text-lg font-normal">
              SMS alerts for critical updates
            </Label>
          </div>
          
          <div className="flex items-center space-x-3">
            <Checkbox 
              id="inapp-notifications"
              checked={settings.inAppNotifications}
              onCheckedChange={(checked) => setSettings({...settings, inAppNotifications: checked as boolean})}
            />
            <Label htmlFor="inapp-notifications" className="text-lg font-normal">
              In-app alerts for product updates
            </Label>
          </div>
        </CardContent>
      </Card>

      <Card className="transition-colors duration-500 ease-in-out">
        <CardHeader>
          <CardTitle className="text-2xl">Access Roles</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-2">
            <Label htmlFor="role" className="text-lg">Default Role</Label>
            <Select 
              value={settings.defaultRole} 
              onValueChange={(value) => setSettings({...settings, defaultRole: value})}
              disabled={!isSuperAdmin}
            >
              <SelectTrigger className="text-lg">
                <SelectValue placeholder="Select a role" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="admin">Admin</SelectItem>
                <SelectItem value="manager">Manager</SelectItem>
                <SelectItem value="staff">Staff</SelectItem>
                <SelectItem value="viewer">Viewer</SelectItem>
              </SelectContent>
            </Select>
            {!isSuperAdmin && (
              <p className="text-sm text-gray-500 dark:text-gray-400">Only Super Admin can modify this setting</p>
            )}
          </div>
        </CardContent>
      </Card>

      <Card className="transition-colors duration-500 ease-in-out">
        <CardHeader>
          <CardTitle className="text-2xl">Export Defaults</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-2">
            <Label htmlFor="export-format" className="text-lg">Default Export Format</Label>
            <Select value={settings.exportFormat} onValueChange={(value) => setSettings({...settings, exportFormat: value})}>
              <SelectTrigger className="text-lg">
                <SelectValue placeholder="Select a format" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="excel">Excel (.xlsx)</SelectItem>
                <SelectItem value="csv">CSV (.csv)</SelectItem>
                <SelectItem value="pdf">PDF (.pdf)</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      <Card className="transition-colors duration-500 ease-in-out">
        <CardHeader>
          <CardTitle className="text-2xl">Workflow Toggles</CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="flex items-center justify-between">
            <div>
              <Label htmlFor="auto-assign" className="text-lg">Auto-Assign Orders</Label>
              <p className="text-base text-gray-500 dark:text-gray-400">
                Automatically assign new orders to available staff
              </p>
              {!isSuperAdmin && (
                <p className="text-sm text-gray-500 dark:text-gray-400">Only Super Admin can modify this setting</p>
              )}
            </div>
            <Switch 
              id="auto-assign"
              checked={settings.autoAssignOrders}
              onCheckedChange={(checked) => setSettings({...settings, autoAssignOrders: checked})}
              disabled={!isSuperAdmin}
            />
          </div>
          
          <div className="flex items-center justify-between">
            <div>
              <Label htmlFor="marketing-banners" className="text-lg">Enable Marketing Banners</Label>
              <p className="text-base text-gray-500 dark:text-gray-400">
                Enable or disable marketing banners on the dashboard
              </p>
              {!isSuperAdmin && (
                <p className="text-sm text-gray-500 dark:text-gray-400">Only Super Admin can modify this setting</p>
              )}
            </div>
            <Switch 
              id="marketing-banners"
              checked={settings.enableMarketingBanners}
              onCheckedChange={(checked) => setSettings({...settings, enableMarketingBanners: checked})}
              disabled={!isSuperAdmin}
            />
          </div>
        </CardContent>
      </Card>

      <div className="flex justify-end">
        <Button 
          onClick={handleSaveChanges}
          className="bg-blue-600 hover:bg-blue-700 transition-all duration-300 ease-in-out text-lg px-8 py-4"
        >
          Save Changes
        </Button>
      </div>
    </div>
  );
};

export default GeneralTab;
