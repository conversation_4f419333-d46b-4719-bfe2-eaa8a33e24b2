
import { useEffect } from 'react';
import { useSidebar } from '@/components/ui/sidebar';

export const useSidebarKeyboard = () => {
  const { toggleSidebar } = useSidebar();

  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      // Only trigger if Enter is pressed and not in an input field
      if (
        event.key === 'Enter' &&
        !(event.target instanceof HTMLInputElement) &&
        !(event.target instanceof HTMLTextAreaElement) &&
        !(event.target instanceof HTMLSelectElement) &&
        !(event.target instanceof Element && event.target.closest('[contenteditable="true"]'))
      ) {
        event.preventDefault();
        toggleSidebar();
      }
    };

    // Add event listener
    window.addEventListener('keydown', handleKeyDown);

    // Cleanup
    return () => {
      window.removeEventListener('keydown', handleKeyDown);
    };
  }, [toggleSidebar]);
};
